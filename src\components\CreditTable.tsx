import { DataTable } from "@/components/ui/data-table";
import React from "react";
import { ColumnDef } from "@tanstack/react-table";

export type CreditRow = {
  id: string;
  title?: string | null;
  duration?: number | null;
  releaseDate?: string | null;
  type?: "song" | "recording";
};

export type CreditTableProps = {
  rows: CreditRow[];
  onRowClick: (row: CreditRow) => void;
};

export const CreditTable: React.FC<CreditTableProps> = ({
  rows,
  onRowClick,
}) => {
  const columns: ColumnDef<CreditRow>[] = [
    {
      accessorKey: "title",
      header: () => <span className="cursor-pointer select-none">Name</span>,
      cell: (info) => {
        const value = info.getValue();
        return value ? <span className="font-medium">{String(value)}</span> : <span className="font-medium">-</span>;
      },
      enableSorting: true,
      size: 300, // Fixed width for consistency
      minSize: 200,
      maxSize: 400,
    },
    {
      accessorKey: "duration",
      header: "Duration",
      cell: (info) => {
        const duration = info.getValue();
        return typeof duration === "number"
          ? `${Math.floor(duration / 1000 / 60)}:${String(Math.floor((duration / 1000) % 60)).padStart(2, "0")}`
          : "-";
      },
      enableSorting: true,
      size: 100, // Fixed width for consistency
      minSize: 80,
      maxSize: 120,
    },
    {
      accessorKey: "releaseDate",
      header: () => <span className="cursor-pointer select-none">Release Date</span>,
      cell: (info) => {
        const date = info.getValue();
        return date ? new Date(date as string).toLocaleDateString() : "-";
      },
      enableSorting: true,
      size: 150, // Fixed width for consistency
      minSize: 120,
      maxSize: 180,
    },
  ];

  return (
    <div className="w-full overflow-x-auto">
      <DataTable
        columns={columns}
        data={rows}
        onRowClick={onRowClick}
        className="w-full border rounded-lg"
      />
    </div>
  );
};
